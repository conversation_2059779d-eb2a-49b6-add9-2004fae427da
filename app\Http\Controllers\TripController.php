<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTripRequest;
use App\Http\Requests\UpdateTripRequest;
use App\Http\Resources\TripResource;
use App\Models\Trip;
use App\Repositories\TripRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TripController extends Controller
{
    private TripRepository $repository;

    public function __construct(TripRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Trip::class, 'trip');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        return TripResource::collection(
            $this->repository
                ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
                ->latest()->where('inter_station', false)
                ->paginate($request->input('perPage'))
        );
    }

    public function all(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->all());
    }

    public function allNotInter(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->where('inter_station', false)->get());
    }

    public function store(StoreTripRequest $request): JsonResponse
    {
        $data = $request->validated();

        // Vérifier si le trajet existe déjà (sens direct)
        $existingTrip = Trip::where('id_line', $data['id_line'])
            ->where('id_station_start', $data['stations']['id_station_start'])
            ->where('id_station_end', $data['stations']['id_station_end'])
            ->where('inter_station', false)
            ->first();

        if ($existingTrip) {
            return response()->json([
                'message' => 'Trip_already_exists',
                'data' => new TripResource($existingTrip)
            ], 409);
        }

        // Vérifier si le trajet inverse existe déjà
        $existingReverseTrip = Trip::where('id_line', $data['id_line'])
            ->where('id_station_start', $data['stations']['id_station_end'])
            ->where('id_station_end', $data['stations']['id_station_start'])
            ->where('inter_station', false)
            ->first();

        if ($existingReverseTrip) {
            return response()->json([
                'message' => 'Reverse_trip_already_exists',
                'error' => 'Un trajet inverse existe déjà pour cette ligne entre ces stations.',
                'existing_trip' => new TripResource($existingReverseTrip),
                'suggestion' => 'Vous pouvez utiliser le trajet existant en activant l\'option "trajet inversé" lors de la création de l\'abonnement.'
            ], 409);
        }

        $trip = $this->repository->create($data);

        return response()->json([
            'message' => 'Trip created successfully',
            'data' => new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']))
        ], 201);
    }

    public function show(Trip $trip): TripResource
    {
        return new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']));
    }

    public function update(UpdateTripRequest $request, Trip $trip): JsonResponse
    {
        try {
            $data = $request->validated();

            // Si les stations ou la ligne sont modifiées, vérifier les doublons
            if (isset($data['stations']) || isset($data['id_line'])) {
                $stationStart = $data['stations']['id_station_start'] ?? $trip->id_station_start;
                $stationEnd = $data['stations']['id_station_end'] ?? $trip->id_station_end;
                $lineId = $data['id_line'] ?? $trip->id_line;

                // Vérifier si un autre trajet existe déjà avec ces paramètres (sens direct)
                $existingTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $stationStart)
                    ->where('id_station_end', $stationEnd)
                    ->where('inter_station', false)
                    ->where('id', '!=', $trip->id) // Exclure le trajet actuel
                    ->first();

                if ($existingTrip) {
                    return response()->json([
                        'message' => 'Trip_already_exists',
                        'error' => 'Un trajet avec ces paramètres existe déjà.',
                        'existing_trip' => new TripResource($existingTrip)
                    ], 409);
                }

                // Vérifier si un trajet inverse existe déjà
                $existingReverseTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $stationEnd)
                    ->where('id_station_end', $stationStart)
                    ->where('inter_station', false)
                    ->where('id', '!=', $trip->id) // Exclure le trajet actuel
                    ->first();

                if ($existingReverseTrip) {
                    return response()->json([
                        'message' => 'Reverse_trip_already_exists',
                        'error' => 'Un trajet inverse existe déjà pour cette ligne entre ces stations.',
                        'existing_trip' => new TripResource($existingReverseTrip),
                        'suggestion' => 'Vous pouvez utiliser le trajet existant en activant l\'option "trajet inversé".'
                    ], 409);
                }
            }

            $trip = $this->repository->update($data, $trip->id);

            return response()->json([
                'message' => 'Trip updated successfully',
                'data' => new TripResource($trip)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Trip $trip): JsonResponse
    {
        try {
            $this->repository->delete($trip->id);

            return response()->json([
                'message' => 'Trip deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed_delete_trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function checkTripExists(Request $request): JsonResponse
    {
        $request->validate([
            'id_line' => 'required|exists:lines,id',
            'id_station_start' => 'required|exists:stations,id',
            'id_station_end' => 'required|exists:stations,id|different:id_station_start'
        ]);

        $lineId = $request->input('id_line');
        $stationStart = $request->input('id_station_start');
        $stationEnd = $request->input('id_station_end');

        $directTrip = Trip::where('id_line', $lineId)
            ->where('id_station_start', $stationStart)
            ->where('id_station_end', $stationEnd)
            ->where('inter_station', false)
            ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
            ->first();

        $reverseTrip = Trip::where('id_line', $lineId)
            ->where('id_station_start', $stationEnd)
            ->where('id_station_end', $stationStart)
            ->where('inter_station', false)
            ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
            ->first();

        $result = [
            'direct_trip_exists' => !is_null($directTrip),
            'reverse_trip_exists' => !is_null($reverseTrip),
            'direct_trip' => $directTrip ? new TripResource($directTrip) : null,
            'reverse_trip' => $reverseTrip ? new TripResource($reverseTrip) : null
        ];

        if ($directTrip || $reverseTrip) {
            $result['message'] = 'Des trajets existent déjà entre ces stations';
            if ($reverseTrip && !$directTrip) {
                $result['suggestion'] = 'Vous pouvez utiliser le trajet inverse existant en activant l\'option "trajet inversé" lors de la création de l\'abonnement.';
            }
        } else {
            $result['message'] = 'Aucun trajet n\'existe entre ces stations sur cette ligne';
            $result['can_create'] = true;
        }

        return response()->json($result);
    }
}




