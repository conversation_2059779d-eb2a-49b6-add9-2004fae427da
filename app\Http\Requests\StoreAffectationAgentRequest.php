<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAffectationAgentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }
 

    public function rules(): array
    {
        return [
            'id_sale_point' => 'required|exists:sale_points,id',
            'id_agent' => 'required|exists:admins,id',
            'id_sale_period' => 'required|exists:sale_periods,id',
            'cardTypes' => 'required|array',
            'cardTypes.*' => 'required|exists:card_types,id',
            'ranges' => 'required|array',
            'ranges.*.cardType' => 'required|exists:card_types,id',
            'ranges.*.start_serial_number' => 'required|integer',
            'ranges.*.end_serial_number' => 'required|integer|gt:ranges.*.start_serial_number',
        ];
    }
}

