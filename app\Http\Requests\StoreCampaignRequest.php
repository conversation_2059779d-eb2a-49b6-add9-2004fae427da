<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCampaignRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'status' => 'boolean'
        ];
    }
}