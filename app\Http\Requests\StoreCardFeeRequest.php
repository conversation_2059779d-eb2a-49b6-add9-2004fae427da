<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCardFeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'amount' => 'required',
            'id_subs_type' => 'required|exists:subs_types,id'
        ];
    }
}