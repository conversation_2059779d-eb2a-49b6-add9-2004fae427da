<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTariffBaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'tariffPerKM' => 'required|numeric|min:0',
            'date' => 'required|date',
            'for_website' => 'boolean',
            'id_subs_type' => 'required_if:for_website,false|prohibited_if:for_website,true|exists:subs_types,id'
        ];
    }
}



