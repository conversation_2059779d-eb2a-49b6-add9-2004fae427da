<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTariffOptionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_trip' => 'required|exists:trips,id',
            'id_subs_type' => 'required|exists:subs_types,id',
            'is_regular' => 'required|boolean',
            'id_tariff_base' => 'required_if:is_regular,true|prohibited_if:is_regular,false|exists:tariff_bases,id',
            'manual_tariff' => 'required_if:is_regular,false|prohibited_if:is_regular,true|numeric|min:0'
        ];
    }
}