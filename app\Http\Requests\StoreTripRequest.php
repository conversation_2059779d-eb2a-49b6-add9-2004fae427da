<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTripRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'required|string|max:255',
            'nom_en' => 'required|string|max:255',
            'nom_ar' => 'required|string|max:255',
            'id_line' => 'required|exists:lines,id',
            'stations' => 'required|array',
            'stations.id_station_start' => 'required|exists:stations,id',
            'stations.id_station_end' => 'required|exists:stations,id|different:stations.id_station_start',
            'status' => 'required|boolean',
            'inter_station' => 'required|boolean',
            'number_of_km' => 'required|numeric|min:0|max:999999.99',
            'tariff_options' => 'required|array',
            'tariff_options.*.id_subs_type' => 'required|exists:subs_types,id',
            'tariff_options.*.is_regular' => 'required|boolean',
            'tariff_options.*.id_tariff_base' => 'required_if:tariff_options.*.is_regular,true|exists:tariff_bases,id',
            'tariff_options.*.manual_tariff' => 'required_if:tariff_options.*.is_regular,false|numeric|min:0'
        ];
    }
}
