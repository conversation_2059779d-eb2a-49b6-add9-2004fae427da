<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAdminRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $adminId = $this->route('admin');

        return [
            'firstname' => 'sometimes|string|max:255',
            'lastname' => 'sometimes|string|max:255',
            'phone' => ['sometimes', 'string', 'max:15', Rule::unique('admins', 'phone')->ignore($adminId)],
            'cin' => ['sometimes', 'string', 'max:8', Rule::unique('admins', 'cin')->ignore($adminId)],
            'address' => 'sometimes|string|max:255',
            'email' => ['sometimes', 'email', 'max:255', Rule::unique('admins', 'email')->ignore($adminId)],
        ];
        if (!is_null($this->input('password'))) {
            $rules['password'] = 'sometimes|string|min:6';
        }
    }
}

