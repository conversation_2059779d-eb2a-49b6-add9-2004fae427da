<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCardFeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'amount' => 'sometimes|required',
            'id_subs_type' => 'sometimes|required|exists:subs_types,id'
        ];
    }
}