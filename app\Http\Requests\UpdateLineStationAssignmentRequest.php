<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLineStationAssignmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id_line' => 'required|exists:lines,id',
            'stations' => 'required|array',
            'stations.*.id_station' => 'required|exists:stations,id',
            'stations.*.position' => 'required|integer|min:1',
            'stations.*.type' => 'required|in:HIDDEN,TERMINUS,INTER',
            'stations.*.departure_times' => 'present|array',
            'stations.*.departure_times.*.id_season' => 'required_with:stations.*.departure_times|exists:seasons,id',
            'stations.*.departure_times.*.times' => 'required_with:stations.*.departure_times|array',
            'stations.*.departure_times.*.times.*' => 'required|string|date_format:H:i',

            'routes' => 'required|array',
            'routes.*.number_of_km' => 'required|numeric|min:0',
            'routes.*.id_station_start' => 'required|exists:stations,id',
            'routes.*.id_station_end' => 'required|exists:stations,id',
            'routes.*.inter_station' => 'required|boolean',
            'routes.*.status' => 'required|boolean'
        ];
    }
}

