<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSalePeriodRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'sometimes|required|string|max:255',
            'nom_ar' => 'sometimes|required|string|max:255',
            'date_start' => 'sometimes|required|date',
            'date_end' => 'sometimes|required|date|after_or_equal:date_start',
            'id_campaign' => 'sometimes|required|exists:campaigns,id',
            'id_abn_type' => 'sometimes|required|exists:subs_types,id',
        ];
    }
}


