<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class AffectationCardType extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'id_card_type',
        'start_serial_number',
        'end_serial_number',
        'current_serial_number',
        'id_affectation_agent',
    ];

    public function subsCards(): HasMany
    {
        return $this->hasMany(SubsCard::class, 'id_affectation_card_type');
    }

    public function cardType(): BelongsTo
    {
        return $this->belongsTo(CardType::class, 'id_card_type');
    }

    public function affectationAgent(): BelongsTo
    {
        return $this->belongsTo(AffectationAgent::class, 'id_affectation_agent');
    }
}

