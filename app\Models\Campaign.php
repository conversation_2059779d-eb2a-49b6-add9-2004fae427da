<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class Campaign extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    public function salePeriods(): HasMany
    {
        return $this->hasMany(SalePeriod::class, 'id_campaign');
    }
}
