<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class LineStation extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'id_line',
        'id_station',
        'position',
        'start_time',
        'type',
        'direction'
    ];

    protected $casts = [
        'start_time' => 'json',
        'position' => 'integer'
    ];

    public function line(): BelongsTo
    {
        return $this->belongsTo(Line::class, 'id_line');
    }

    public function station(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station');
    }
}
