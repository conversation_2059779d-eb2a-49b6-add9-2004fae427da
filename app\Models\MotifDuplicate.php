<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class MotifDuplicate extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'id_card_type'
    ];

    public function subsCards(): HasMany
    {
        return $this->hasMany(SubsCard::class, 'id_motif_duplicate');
    }

    public function cardType()
    {
        return $this->belongsTo(CardType::class, 'id_card_type');
    }

    /**
     * Get all of the subs_duplications for the MotifDuplicate
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subs_duplications(): HasMany
    {
        return $this->hasMany(SubsDuplication::class, 'motif_duplicate_id', 'id');
    }


}


