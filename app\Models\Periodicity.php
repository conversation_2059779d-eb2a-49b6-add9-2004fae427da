<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class Periodicity extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'periodicity_code',
        'max_days_per_week'
    ];

    protected $casts = [
        'max_days_per_week' => 'integer'
    ];
}
