<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class TariffBase extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'tariffPerKM',
        'date',
        'id_subs_type',
        'for_website'
    ];

    protected $casts = [
        'date' => 'date',
        'tariffPerKM' => 'decimal:2',
        'for_website' => 'boolean'
    ];

    public function tariffOptions(): HasMany
    {
        return $this->hasMany(TariffOption::class, 'id_tariff_base');
    }

    public function subsType(): BelongsTo
    {
        return $this->belongsTo(SubsType::class, 'id_subs_type');
    }
}





