<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SubsCard;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubsCardPolicy
{
    use HandlesAuthorization;

    public function viewAny(Admin $admin): bool
    {
        return $admin->can('view_subs_cards');
    }

    public function view(Admin $admin, SubsCard $subsCard): bool
    {
        return $admin->can('view_subs_cards');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_subs_cards');
    }

    public function update(Admin $admin, SubsCard $subsCard): bool
    {
        return $admin->can('edit_subs_cards');
    }

    public function delete(Admin $admin, SubsCard $subsCard): bool
    {
        return $admin->can('delete_subs_cards');
    }
}