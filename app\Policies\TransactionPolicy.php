<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Transaction;

class TransactionPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('manage_transactions');
    }

    public function view(Admin $admin, Transaction $transaction): bool
    {
        return $admin->can('view_transactions');
    }

    public function create(Admin $admin): bool
    {
        return $admin->can('create_transactions');
    }

    public function update(Admin $admin, Transaction $transaction): bool
    {
        return $admin->can('edit_transactions');
    }

    public function delete(Admin $admin, Transaction $transaction): bool
    {
        return $admin->can('delete_transactions');
    }
}
