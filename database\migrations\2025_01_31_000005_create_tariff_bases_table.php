<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tariff_bases', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->boolean('for_website')->default(false);
            $table->decimal('tariffPerKM', 10, 2);
            $table->date('date');
            $table->foreignId('id_subs_type')->nullable()->constrained('subs_types');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tariff_bases');
    }
};