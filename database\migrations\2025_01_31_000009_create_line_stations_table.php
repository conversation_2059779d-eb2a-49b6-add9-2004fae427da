<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('line_stations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_line')->constrained('lines');
            $table->foreignId('id_station')->constrained('stations');
            $table->integer('position');
            $table->enum('type', ['HIDDEN', 'TERMINUS', 'INTER'])->default('INTER');
            $table->integer('direction')->nullable();
            $table->json('start_time')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('line_stations');
    }
};
