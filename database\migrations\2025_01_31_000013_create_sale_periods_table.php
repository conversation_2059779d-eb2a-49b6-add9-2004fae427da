<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sale_periods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('date_start');
            $table->date('date_end');
            $table->foreignId('id_campaign')->constrained('campaigns');
            $table->foreignId('id_abn_type')->constrained('subs_types');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sale_periods');
    }
};

