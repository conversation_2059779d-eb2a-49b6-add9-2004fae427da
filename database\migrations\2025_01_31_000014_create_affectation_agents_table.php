<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('affectation_agents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_agent')->constrained('admins');
            $table->foreignId('id_sale_point')->constrained('sale_points');
            $table->foreignId('id_sale_period')->constrained('sale_periods');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('affectation_agents');
    }
};
