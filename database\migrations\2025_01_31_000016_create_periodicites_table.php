<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('periodicities', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('periodicity_code');
            $table->integer('max_days_per_week');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('periodicities');
    }
};
