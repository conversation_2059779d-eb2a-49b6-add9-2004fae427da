<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('motif_duplicates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('id_card_type')->constrained('card_types');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('motif_duplicates');
    }
};