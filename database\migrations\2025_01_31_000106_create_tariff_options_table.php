<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tariff_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_trip')->constrained('trips');
            $table->foreignId('id_subs_type')->constrained('subs_types');
            $table->boolean('is_regular')->default(true);
            $table->foreignId('id_tariff_base')->nullable()->constrained('tariff_bases');
            $table->integer('manual_tariff')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tariff_options');
    }
};
