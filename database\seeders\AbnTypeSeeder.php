<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AbnTypeSeeder extends Seeder
{
    public function run(): void
    {
        $abnTypes = [
            [
                'nom_fr' => 'Abonnement scolaire',
                'nom_en' => 'School Subscription',
                'nom_ar' => 'اشتراك مدرسي',
                'color' => '#FF5733',
                'is_student' => true,
                'hasCIN' => false,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement universitaire',
                'nom_en' => 'University Subscription',
                'nom_ar' => 'اشتراك جامعي',
                'color' => '#33FF57',
                'is_student' => true,
                'hasCIN' => true,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement civil',
                'nom_en' => 'Civil Subscription',
                'nom_ar' => 'اشتراك مدني',
                'color' => '#3357FF',
                'is_student' => false,
                'hasCIN' => true,
                'is_impersonal' => false,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement impersonnel',
                'nom_en' => 'Impersonal Subscription',
                'nom_ar' => 'اشتراك غير شخصي',
                'color' => '#C70039',
                'is_student' => false,
                'hasCIN' => false,
                'is_impersonal' => true,
                'is_conventional' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nom_fr' => 'Abonnement conventionné',
                'nom_en' => 'Contracted subscription',
                'nom_ar' => 'اشتراك تعاقدي',
                'color' => '#888039',
                'is_student' => false,
                'hasCIN' => false,
                'is_impersonal' => false,
                'is_conventional' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('subs_types')->insert($abnTypes);
    }
}

