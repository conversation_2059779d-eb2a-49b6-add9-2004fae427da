<?php

namespace Database\Seeders;

use App\Models\Campaign;
use Illuminate\Database\Seeder;

class CampaignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define campaign data
        $campaigns = [
            [
                'nom_fr' => 'Campagne Rentrée Scolaire 2025',
                'nom_en' => 'Back to School Campaign 2025',
                'nom_ar' => 'حملة العودة إلى المدرسة 2025',
                'status' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Été 2025',
                'nom_en' => 'Summer Campaign 2025',
                'nom_ar' => 'حملة صيف 2025',
                'status' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Hiver 2025',
                'nom_en' => 'Winter Campaign 2025',
                'nom_ar' => 'حملة شتاء 2025',
                'status' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Spéciale Étudiants',
                'nom_en' => 'Special Student Campaign',
                'nom_ar' => 'حملة خاصة للطلاب',
                'status' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'nom_fr' => 'Campagne Promotionnelle',
                'nom_en' => 'Promotional Campaign',
                'nom_ar' => 'حملة ترويجية',
                'status' => false,
                'created_at' => now(),
                'updated_at' => now()
            ],
        ];

        // Insert campaigns
        foreach ($campaigns as $campaign) {
            Campaign::create($campaign);
        }
    }
}
