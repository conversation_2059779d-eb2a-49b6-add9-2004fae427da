<?php

namespace Database\Seeders;

use App\Models\CardFee;
use App\Models\SubsType;
use Illuminate\Database\Seeder;

class CardFeesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all subscription types
        $subsTypes = SubsType::all();
        
        $cardFees = [
            // School Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte scolaire - Première émission',
                'nom_en' => 'School Card Fee - First Issue',
                'nom_ar' => 'رسوم البطاقة المدرسية - الإصدار الأول',
                'amount' => 2.00,
                'subs_type_index' => 0 // School subscription
            ],
            [
                'nom_fr' => 'Frais de carte scolaire - Duplicata',
                'nom_en' => 'School Card Fee - Duplicate',
                'nom_ar' => 'رسوم البطاقة المدرسية - نسخة مكررة',
                'amount' => 3.00,
                'subs_type_index' => 0 // School subscription
            ],

            // University Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte universitaire - Première émission',
                'nom_en' => 'University Card Fee - First Issue',
                'nom_ar' => 'رسوم البطاقة الجامعية - الإصدار الأول',
                'amount' => 2.50,
                'subs_type_index' => 1 // University subscription
            ],
            [
                'nom_fr' => 'Frais de carte universitaire - Duplicata',
                'nom_en' => 'University Card Fee - Duplicate',
                'nom_ar' => 'رسوم البطاقة الجامعية - نسخة مكررة',
                'amount' => 3.50,
                'subs_type_index' => 1 // University subscription
            ],

            // Civil Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte civile - Première émission',
                'nom_en' => 'Civil Card Fee - First Issue',
                'nom_ar' => 'رسوم البطاقة المدنية - الإصدار الأول',
                'amount' => 3.00,
                'subs_type_index' => 2 // Civil subscription
            ],
            [
                'nom_fr' => 'Frais de carte civile - Duplicata',
                'nom_en' => 'Civil Card Fee - Duplicate',
                'nom_ar' => 'رسوم البطاقة المدنية - نسخة مكررة',
                'amount' => 4.00,
                'subs_type_index' => 2 // Civil subscription
            ],

            // Impersonal Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte impersonnelle - Première émission',
                'nom_en' => 'Impersonal Card Fee - First Issue',
                'nom_ar' => 'رسوم البطاقة غير الشخصية - الإصدار الأول',
                'amount' => 3.50,
                'subs_type_index' => 3 // Impersonal subscription
            ],
            [
                'nom_fr' => 'Frais de carte impersonnelle - Duplicata',
                'nom_en' => 'Impersonal Card Fee - Duplicate',
                'nom_ar' => 'رسوم البطاقة غير الشخصية - نسخة مكررة',
                'amount' => 4.50,
                'subs_type_index' => 3 // Impersonal subscription
            ],

            // Contracted Subscription Card Fees
            [
                'nom_fr' => 'Frais de carte conventionnée - Première émission',
                'nom_en' => 'Contracted Card Fee - First Issue',
                'nom_ar' => 'رسوم البطاقة التعاقدية - الإصدار الأول',
                'amount' => 4.00,
                'subs_type_index' => 4 // Contracted subscription
            ],
            [
                'nom_fr' => 'Frais de carte conventionnée - Duplicata',
                'nom_en' => 'Contracted Card Fee - Duplicate',
                'nom_ar' => 'رسوم البطاقة التعاقدية - نسخة مكررة',
                'amount' => 5.00,
                'subs_type_index' => 4 // Contracted subscription
            ]
        ];

        foreach ($cardFees as $cardFee) {
            if (isset($subsTypes[$cardFee['subs_type_index']])) {
                CardFee::create([
                    'nom_fr' => $cardFee['nom_fr'],
                    'nom_en' => $cardFee['nom_en'],
                    'nom_ar' => $cardFee['nom_ar'],
                    'amount' => $cardFee['amount'],
                    'id_subs_type' => $subsTypes[$cardFee['subs_type_index']]->id
                ]);
            }
        }
    }
}

