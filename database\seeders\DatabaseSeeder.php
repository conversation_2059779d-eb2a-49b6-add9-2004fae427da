<?php

namespace Database\Seeders;

use Database\Seeders\website\WebsiteTripsSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            SuperAdminSeeder::class,
            AbnTypeSeeder::class,
            LineSeeder::class,
            GovernorateDelegationSeeder::class,
            StationSeeder::class,
            SeasonSeeder::class,
            TypeClientSeeder::class,
            PeriodicitySeeder::class,
            WebsiteTripsSeeder::class,
            InterStationTripsSeeder::class,
            LocationTypeSeeder::class,
            TypeVehiculeSeeder::class,
            TypeVehicleTypeLocationSeeder::class,
            LocationSeasonSeeder::class,
            TypeVehiculeSaisonLocationSeeder::class,
            AcademicYearSeeder::class,
            SocialAffairSeeder::class,
            CampaignSeeder::class,
            SalePeriodSeeder::class,
            CardTypeSeeder::class,
            TypeEstablishmentSeeder::class,
            DegreeSeeder::class,
            EstablishmentSeeder::class,
            AgencySeeder::class,
            SalePointSeeder::class,
            PaymentMethodSeeder::class,
            TariffBasesSeeder::class,
            CardFeesSeeder::class,
            MotifDuplicaSeeder::class,
        ]);
    }
}



