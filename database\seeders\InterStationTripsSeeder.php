<?php

namespace Database\Seeders;

use App\Models\Line;
use App\Models\Station;
use App\Models\LineStation;
use App\Models\Trip;
use App\Models\WebsiteTrip;
use Illuminate\Database\Seeder;

class InterStationTripsSeeder extends Seeder
{
    public function run(): void
    {
        // Get all lines
        $lines = Line::all();

        foreach ($lines as $line) {
            // Get all stations for this line ordered by position
            $lineStations = LineStation::where('id_line', $line->id)
                ->orderBy('position')
                ->get();

            // Create trips between consecutive stations
            for ($i = 0; $i < $lineStations->count() - 1; $i++) {
                $currentStation = $lineStations[$i];
                $nextStation = $lineStations[$i + 1];

                // Calculate estimated number of km (you might want to adjust this logic)
                $startStation = Station::find($currentStation->id_station);
                $endStation = Station::find($nextStation->id_station);

                // Get number_of_km from website_trips
                $websiteTrip = WebsiteTrip::where([
                    'id_line' => $line->id,
                    'id_station_start' => $currentStation->id_station,
                    'id_station_end' => $nextStation->id_station,
                ])->first();

                // Skip if no corresponding website_trip found
                if (!$websiteTrip) {
                    echo "No website trip found for: {$startStation->nom_fr} - {$endStation->nom_fr} (Line: {$line->id})\n";
                    continue;
                }

                // Create the trip
                Trip::create([
                    'nom_fr' => $startStation->nom_fr . ' - ' . $endStation->nom_fr,
                    'nom_en' => $startStation->nom_en . ' - ' . $endStation->nom_en,
                    'nom_ar' => $startStation->nom_ar . ' - ' . $endStation->nom_ar,
                    'id_line' => $line->id,
                    'id_station_start' => $currentStation->id_station,
                    'id_station_end' => $nextStation->id_station,
                    'status' => true,
                    'inter_station' => true,
                    'number_of_km' => $websiteTrip->number_of_km
                ]);
            }
        }
    }
}


