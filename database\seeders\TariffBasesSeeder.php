<?php

namespace Database\Seeders;

use App\Models\SubsType;
use App\Models\TariffBase;
use Illuminate\Database\Seeder;

class TariffBasesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all subscription types
        $subsTypes = SubsType::all();
        
        $tariffBases = [
            // School Subscription Tariffs
            [
                'nom_fr' => 'Tarif de base scolaire - Standard',
                'nom_en' => 'School Base Tariff - Standard',
                'nom_ar' => 'التعريفة الأساسية المدرسية - عادية',
                'tariffPerKM' => 1.150,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 0 // School subscription
            ],
            [
                'nom_fr' => 'Tarif de base scolaire - Premium',
                'nom_en' => 'School Base Tariff - Premium',
                'nom_ar' => 'التعريفة الأساسية المدرسية - مميزة',
                'tariffPerKM' => 1.200,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 0 // School subscription
            ],

            // University Subscription Tariffs
            [
                'nom_fr' => 'Tarif de base universitaire - Standard',
                'nom_en' => 'University Base Tariff - Standard',
                'nom_ar' => 'التعريفة الأساسية الجامعية - عادية',
                'tariffPerKM' => 1.175,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 1 // University subscription
            ],
            [
                'nom_fr' => 'Tarif de base universitaire - Premium',
                'nom_en' => 'University Base Tariff - Premium',
                'nom_ar' => 'التعريفة الأساسية الجامعية - مميزة',
                'tariffPerKM' => 1.225,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 1 // University subscription
            ],

            // Civil Subscription Tariffs
            [
                'nom_fr' => 'Tarif de base civil - Standard',
                'nom_en' => 'Civil Base Tariff - Standard',
                'nom_ar' => 'التعريفة الأساسية المدنية - عادية',
                'tariffPerKM' => 1.200,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 2 // Civil subscription
            ],
            [
                'nom_fr' => 'Tarif de base civil - Premium',
                'nom_en' => 'Civil Base Tariff - Premium',
                'nom_ar' => 'التعريفة الأساسية المدنية - مميزة',
                'tariffPerKM' => 1.250,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 2 // Civil subscription
            ],

            // Impersonal Subscription Tariffs
            [
                'nom_fr' => 'Tarif de base impersonnel',
                'nom_en' => 'Impersonal Base Tariff',
                'nom_ar' => 'التعريفة الأساسية غير الشخصية',
                'tariffPerKM' => 1.300,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 3 // Impersonal subscription
            ],

            // Contracted Subscription Tariffs
            [
                'nom_fr' => 'Tarif de base conventionné',
                'nom_en' => 'Contracted Base Tariff',
                'nom_ar' => 'التعريفة الأساسية التعاقدية',
                'tariffPerKM' => 1.275,
                'date' => '2025-01-01',
                'for_website' => false,
                'subs_type_index' => 4 // Contracted subscription
            ],

            // Website Tariffs (no subscription type)
            [
                'nom_fr' => 'Tarif de base site web',
                'nom_en' => 'Website Base Tariff',
                'nom_ar' => 'التعريفة الأساسية للموقع',
                'tariffPerKM' => 1.350,
                'date' => '2025-01-01',
                'for_website' => true,
                'subs_type_index' => null
            ]
        ];

        foreach ($tariffBases as $tariffBase) {
            TariffBase::create([
                'nom_fr' => $tariffBase['nom_fr'],
                'nom_en' => $tariffBase['nom_en'],
                'nom_ar' => $tariffBase['nom_ar'],
                'tariffPerKM' => $tariffBase['tariffPerKM'],
                'date' => $tariffBase['date'],
                'for_website' => $tariffBase['for_website'],
                'id_subs_type' => $tariffBase['subs_type_index'] !== null 
                    ? $subsTypes[$tariffBase['subs_type_index']]->id 
                    : null
            ]);
        }
    }
}

