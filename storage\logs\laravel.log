[2025-06-12 21:41:23] local.INFO: User Roles: ["SUPER_VENDEUR"] 
[2025-06-12 21:41:23] local.INFO: User Permissions: ["manage_dashboard","view_dashboard","create_dashboard","edit_dashboard","delete_dashboard","manage_payment_methods","view_payment_methods","edit_payment_methods","manage_newSubs","view_newSubs","create_newSubs","edit_newSubs","delete_newSubs","manage_editing_payed_subscriptions","manage_cancel_transactions"] 
[2025-06-12 21:41:23] local.ERROR: Call to undefined method App\Models\Admin::hasPermissionTo() {"userId":2,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Admin::hasPermissionTo() at C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('hasPermissionTo')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'hasPermissionTo', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\app\\Models\\Admin.php(33): Illuminate\\Database\\Eloquent\\Model->__call('hasPermissionTo', Array)
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\app\\Http\\Controllers\\PaymentMethodController.php(53): App\\Models\\Admin->hasPermissionTo('edit_newSubs')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\PaymentMethodController->update(Object(App\\Http\\Requests\\UpdatePaymentMethodRequest), Object(App\\Models\\PaymentMethod))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('update', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PaymentMethodController), 'update')
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\tymon\\jwt-auth\\src\\Http\\Middleware\\Authenticate.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Tymon\\JWTAuth\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\app\\Http\\Middleware\\SetLanguage.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLanguage->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-private\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}
"} 
